import { system } from '@minecraft/server';
export function initIllagerTowerTurrets(location, dimension) {
    try {
        const turretLocation = {
            x: location.x,
            y: location.y + 1,
            z: location.z
        };
        dimension.spawnEntity('rza:arrow_turret', turretLocation);
    }
    catch (error) {
        console.warn(`Failed to spawn arrow turret: ${error}`);
    }
    return;
}
export async function spawnTowerGuards(location, dimension) {
    const guardCount = 3;
    for (let i = 0; i < guardCount; i++) {
        const offset = {
            x: Math.random() * 2 - 1,
            z: Math.random() * 2 - 1
        };
        const guardLocation = {
            x: location.x + offset.x,
            y: location.y + 1,
            z: location.z + offset.z
        };
        try {
            dimension.spawnEntity('minecraft:pillager<rza:as_tower_guard>', guardLocation);
            await system.waitTicks(1);
        }
        catch (error) {
            console.warn(`Failed to spawn tower guard: ${error}`);
        }
    }
    return;
}
