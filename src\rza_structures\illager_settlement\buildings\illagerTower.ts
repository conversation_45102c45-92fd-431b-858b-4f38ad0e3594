import { Dimension, Vector3, system } from '@minecraft/server';

export function initIllagerTowerTurrets(location: Vector3, dimension: Dimension): void {
    try {
        const turretLocation: Vector3 = {
            x: location.x,
            y: location.y + 1,
            z: location.z
        };
        dimension.spawnEntity('rza:arrow_turret' as any, turretLocation);
    } catch (error) {
        console.warn(`Failed to spawn arrow turret: ${error}`);
    }
    return;
}

export async function spawnTowerGuards(location: Vector3, dimension: Dimension): Promise<void> {
    // Number of guards to spawn
    const guardCount = 3;

    // Spawn multiple guards with slight position variations
    for (let i = 0; i < guardCount; i++) {
        // Add small random offset to prevent guards from spawning in the same spot
        const offset = {
            x: Math.random() * 2 - 1, // Random value between -1 and 1
            z: Math.random() * 2 - 1
        };

        const guardLocation: Vector3 = {
            x: location.x + offset.x,
            y: location.y + 1,
            z: location.z + offset.z
        };

        try {
            // Spawn pillager with tower guard event
            dimension.spawnEntity('minecraft:pillager<rza:as_tower_guard>' as any, guardLocation);
            await system.waitTicks(1); // Wait 1 tick before next spawn
        } catch (error) {
            console.warn(`Failed to spawn tower guard: ${error}`);
        }
    }
    return;
}
